#!/usr/bin/env python3
"""
Simple MCP HTTP Server for Level 0031
Provides basic MCP tools over HTTP for Django integration
"""

import os
import sys
import logging
import argparse

# Setup Django environment before importing Django-dependent modules
def setup_django():
    """Setup Django environment for MCP server"""
    try:
        # Add the project root to Python path
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)

        # Add the gaia directory to Python path for gaia_ceto_v2 imports
        gaia_dir = os.path.join(project_root, 'gaia')
        if gaia_dir not in sys.path:
            sys.path.insert(0, gaia_dir)

        # Add the djangaia directory to Python path for Django apps
        djangaia_dir = os.path.join(project_root, 'gaia', 'djangaia')
        if djangaia_dir not in sys.path:
            sys.path.insert(0, djangaia_dir)

        # Setup Django if not already configured
        import django
        from django.conf import settings
        if not settings.configured:
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'djangaia.settings')
            django.setup()

        return True
    except Exception as e:
        print(f"Django setup error: {e}")
        return False

# Setup Django before importing other modules
setup_django()


# Minimal Django setup: rely on proper packaging and env var
#os.environ.setdefault("DJANGO_SETTINGS_MODULE", "djangaia.settings")
#import django
#django.setup()
    
from fastmcp import FastMCP

# Import core and tools using absolute imports
from gaia_ceto_v2.core.mcp_wrapper import log_mcp_tool_call
from gaia_ceto_v2.tools.tools import echostring as core_echostring

logger = logging.getLogger(__name__)

mcp = FastMCP("gaia_ceto_v2_http_server")

@mcp.tool()
def echostring(text: str) -> str:
    """
    Echoes the given text.
    """
    # Level 0007: Log MCP tool call
    with log_mcp_tool_call("echostring", {"text": text}, server_name="gaia_ceto_v2") as call:
        result = core_echostring(text)
        call.log_success(result)
        return result


def main():
    """Main entry point for HTTP MCP server."""
    parser = argparse.ArgumentParser(description="Gaia CETO v2 MCP HTTP Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=9000, help="Port to bind to")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    
    args = parser.parse_args()
    
    # Configure logging
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger.info("=" * 60)
    logger.info("🚀 GAIA CETO v2 MCP HTTP SERVER - Level 0031")
    logger.info("=" * 60)
    logger.info(f"🌐 Server URL: http://{args.host}:{args.port}/mcp")
    logger.info(f"🔧 Available tools: echostring, test_tool, server_info")
    logger.info(f"📋 Purpose: Django ceto_chat integration")
    logger.info("=" * 60)
    
    try:
        # Create HTTP app and run with uvicorn
        import uvicorn
        app = mcp.streamable_http_app()
        
        uvicorn.run(
            app,
            host=args.host,
            port=args.port,
            log_level="info" if not args.debug else "debug"
        )
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        raise

if __name__ == "__main__":
    main()
